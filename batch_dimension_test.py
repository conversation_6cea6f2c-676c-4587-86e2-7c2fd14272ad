"""
Batch维度处理验证测试
验证实时推理函数中batch维度的正确处理
"""

import torch
import numpy as np
from model_load import data_add_channel, test_batch_dimensions

def detailed_batch_test():
    """
    详细的batch维度测试
    """
    print("=== 详细的Batch维度处理测试 ===\n")
    
    # 1. 创建模拟的特征数据
    print("1. 创建模拟特征数据")
    ART_feature = np.random.randn(64, 64, 64).astype(np.float32)
    DT_feature = np.random.randn(64, 64).astype(np.float32)
    ERT_feature = np.random.randn(64, 64, 64).astype(np.float32)
    RT_feature = np.random.randn(64, 64).astype(np.float32)
    RDT_feature = np.random.randn(64, 64, 64).astype(np.float32)
    
    print("原始特征形状:")
    features = {
        'ART': ART_feature,
        'DT': DT_feature,
        'ERT': ERT_feature,
        'RT': RT_feature,
        'RDT': RDT_feature
    }
    
    for name, feature in features.items():
        print(f"  {name}_feature: {feature.shape}")
    
    # 2. data_add_channel处理
    print("\n2. data_add_channel处理")
    ART_processed, DT_processed, ERT_processed, RT_processed, RDT_processed = data_add_channel(
        ART_feature, DT_feature, ERT_feature, RT_feature, RDT_feature
    )
    
    processed_features = {
        'ART': ART_processed,
        'DT': DT_processed,
        'ERT': ERT_processed,
        'RT': RT_processed,
        'RDT': RDT_processed
    }
    
    print("data_add_channel处理后:")
    for name, feature in processed_features.items():
        print(f"  {name}_processed: {feature.shape}")
    
    # 3. 转换为torch张量并添加batch维度
    print("\n3. 转换为torch张量并添加batch维度")
    device = torch.device('cpu')
    
    torch_features = {}
    for name, feature in processed_features.items():
        # 转换为torch张量
        tensor = torch.from_numpy(feature).float()
        print(f"  {name} torch张量: {tensor.shape}")
        
        # 添加batch维度
        tensor_with_batch = tensor.unsqueeze(0)
        torch_features[name] = tensor_with_batch.to(device)
        print(f"  {name} 添加batch维度后: {tensor_with_batch.shape}")
    
    # 4. 验证batch维度处理
    print("\n4. 验证batch维度处理")
    print("最终的torch张量形状 (用于模型输入):")
    for name, tensor in torch_features.items():
        print(f"  {name}: {tensor.shape}")
        
        # 验证第一个维度是batch维度
        assert tensor.shape[0] == 1, f"{name}的batch维度不正确"
        print(f"  ✓ {name} batch维度正确 (batch_size=1)")
    
    # 5. 模拟模型输出处理
    print("\n5. 模拟模型输出处理")
    
    # 模拟模型输出 (batch_size=1, num_classes=7)
    mock_outputs = torch.randn(1, 7)
    print(f"模拟模型输出形状: {mock_outputs.shape}")
    
    # 获取预测类别
    predicted_class = torch.argmax(mock_outputs, dim=-1).cpu().numpy()
    print(f"预测类别 (带batch维度): {predicted_class}, 形状: {predicted_class.shape}")
    
    # 提取标量值
    if predicted_class.shape == (1,):
        predicted_class_scalar = predicted_class[0]
        print(f"预测类别 (标量): {predicted_class_scalar}")
    
    # 获取置信度
    confidence_score = torch.max(torch.softmax(mock_outputs, dim=-1), dim=-1)[0].cpu().numpy()
    print(f"置信度 (带batch维度): {confidence_score}, 形状: {confidence_score.shape}")
    
    if confidence_score.shape == (1,):
        confidence_scalar = confidence_score[0]
        print(f"置信度 (标量): {confidence_scalar:.4f}")
    
    print("\n✅ 所有batch维度处理测试通过！")

def compare_with_training_batch():
    """
    与训练时的batch处理进行对比
    """
    print("\n=== 与训练时batch处理对比 ===")
    
    print("训练时 (validate_model_feature):")
    print("- 数据从DataLoader获取，已经包含batch维度")
    print("- features_list中的每个特征都有batch维度")
    print("- 例如: features_list[0].shape = [batch_size, ...]")
    
    print("\n实时推理时 (realtime_inference):")
    print("- 输入是单个样本的特征数据，没有batch维度")
    print("- 需要手动添加batch维度 (unsqueeze(0))")
    print("- 处理后: tensor.shape = [1, ...]")
    
    print("\n输出处理:")
    print("- 训练时: 输出形状 [batch_size, num_classes]")
    print("- 实时推理: 输出形状 [1, num_classes]")
    print("- 需要提取第一个元素得到标量结果")

def memory_efficiency_tips():
    """
    内存效率提示
    """
    print("\n=== 内存效率提示 ===")
    
    print("1. 使用torch.no_grad():")
    print("   - 禁用梯度计算，节省内存")
    print("   - 推理时不需要计算梯度")
    
    print("\n2. 及时释放张量:")
    print("   - 使用.cpu()将张量移回CPU")
    print("   - 避免GPU内存累积")
    
    print("\n3. 批处理优化:")
    print("   - 如果有多个样本，可以组成batch一起处理")
    print("   - 但实时应用通常是单样本处理")
    
    print("\n4. 数据类型优化:")
    print("   - 使用float32而不是float64")
    print("   - 确保输入数据类型一致")

if __name__ == "__main__":
    # 运行基础测试
    test_batch_dimensions()
    
    # 运行详细测试
    detailed_batch_test()
    
    # 对比说明
    compare_with_training_batch()
    
    # 内存效率提示
    memory_efficiency_tips()
    
    print("\n" + "="*50)
    print("🎉 所有测试完成！batch维度处理正确！")
    print("="*50)
