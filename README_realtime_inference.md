# 实时推理函数使用说明

基于 `validate_model_feature` 函数创建的实时推理函数，用于替换现有的 `Judge_gesture` 函数。

## 新增函数

### 1. `realtime_inference(args, model, ART_feature, DT_feature, ERT_feature, RT_feature, RDT_feature)`

基础实时推理函数，返回原始模型输出和置信度。

**参数:**
- `args`: 模型配置参数
- `model`: 已加载的模型
- `ART_feature`: ART特征数据 (numpy array) - 自动添加batch维度
- `DT_feature`: DT特征数据 (numpy array) - 自动添加batch维度
- `ERT_feature`: ERT特征数据 (numpy array) - 自动添加batch维度
- `RT_feature`: RT特征数据 (numpy array) - 自动添加batch维度
- `RDT_feature`: RDT特征数据 (numpy array) - 自动添加batch维度

**返回:**
- `outputs`: 模型预测输出 (batch_size=1)
- `confidence`: 预测置信度

**特征处理流程:**
1. 使用 `data_add_channel()` 处理特征维度
2. 转换为torch张量并自动添加batch维度 (unsqueeze(0))
3. 进行minmax归一化
4. 执行模型推理

### 2. `realtime_inference_with_prediction(args, model, ART_feature, DT_feature, ERT_feature, RT_feature, RDT_feature, gesture_dict=None)`

完整的实时推理函数，返回预测类别和手势名称。

**参数:**
- 同上，额外参数：
- `gesture_dict`: 手势类别字典，用于将预测索引转换为手势名称

**返回:**
- `predicted_class`: 预测的类别索引
- `predicted_gesture`: 预测的手势名称
- `confidence_score`: 预测置信度分数
- `raw_outputs`: 原始模型输出

### 3. `simple_realtime_inference(model, args, RT_feature, DT_feature, RDT_feature, ART_feature, ERT_feature, gesture_dict=None, confidence_threshold=0.5)`

简化的实时推理函数，可以直接替换 `Judge_gesture` 函数。

**参数:**
- `model`: 已加载的模型
- `args`: 模型配置参数
- 特征参数同上
- `gesture_dict`: 手势类别字典
- `confidence_threshold`: 置信度阈值，低于此值返回'NO'

**返回:**
- `predicted_gesture`: 预测的手势名称

## 使用步骤

### 1. 配置模型参数

```python
import argparse

def create_model_args():
    args = argparse.Namespace()

    # 设备配置
    args.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 模型配置参数 (需要根据实际训练配置调整)
    args.backbone = 'custom'
    args.cnn_output_size = 128
    args.hidden_size = 128
    args.rnn_type = 'lstm'
    args.lstm_layers = 1
    args.bidirectional = True
    args.fc_size = 7
    args.num_domains = 5
    args.fusion_mode = 'attention'  # 或 'concatenate'
    args.method = 'DScombine'  # 或其他方法
    args.is_sharedspecific = 0
    args.bottleneck_dim = None
    args.selected_features = ['RT', 'DT', 'RDT', 'ERT', 'ART']
    args.is_domain_loss = 0

    # 输入特征形状 (需要根据实际数据调整)
    args.input_feature_shapes = {
        'RT': (1, 64, 64),    # RT特征形状
        'DT': (1, 64, 64),    # DT特征形状
        'RDT': (64, 64, 64),  # RDT特征形状
        'ERT': (64, 64, 64),  # ERT特征形状
        'ART': (64, 64, 64)   # ART特征形状
    }

    args.optional_features = ['RT', 'DT', 'RDT', 'ERT', 'ART']

    return args
```

### 2. 加载模型

```python
from model_load import model_load

# 创建配置参数
args = create_model_args()

# 加载模型
model_path = "path/to/your/best_model.pth"
model = model_load(args, model_path, args.device)
```

### 3. 替换 Judge_gesture 函数

在 `main.py` 中，将原来的：

```python
result = Judge_gesture(RT_feature, DT_feature, RDT_feature, ART_feature, ERT_feature)
```

替换为：

```python
from model_load import simple_realtime_inference

# 手势类别字典
gesturedict = {
    '0': 'backward', '1': 'dbclick', '2': 'down', '3': 'front',
    '4': 'Left', '5': 'Right', '6': 'up', '7': 'NO'
}

result = simple_realtime_inference(
    model, args, RT_feature, DT_feature, RDT_feature,
    ART_feature, ERT_feature, gesturedict, confidence_threshold=0.5
)
```

### 4. 完整的集成示例

```python
# 在 main.py 的开头添加导入
from model_load import simple_realtime_inference, model_load
import argparse

# 全局变量
model = None
args = None

# 创建配置函数
def create_model_args():
    # ... (如上所示)
    pass

# 修改 loadmodel 函数
def loadmodel():
    global model, args
    if (modelfile.currentText() != '--select--' and modelfile.currentText() != ''):
        try:
            args = create_model_args()
            model = model_load(args, modelfile.currentText(), args.device)
            printlog('加载' + modelfile.currentText() + '模型成功!', fontcolor='blue')
        except Exception as e:
            printlog(f"模型加载失败: {str(e)}", fontcolor='red')
    else:
        printlog("请加载模型!", fontcolor='red')

# 修改 Judge_gesture 函数
def Judge_gesture(RT_feature, DT_feature, RDT_feature, ART_feature, ERT_feature):
    global model, args

    if model is None:
        printlog("请先加载模型!", fontcolor='red')
        return gesturedict['7']  # 返回'NO'

    try:
        result = simple_realtime_inference(
            model, args, RT_feature, DT_feature, RDT_feature,
            ART_feature, ERT_feature, gesturedict, confidence_threshold=0.5
        )

        # 更新UI显示
        gesture_class = None
        for key, value in gesturedict.items():
            if value == result:
                gesture_class = key
                break

        if gesture_class:
            view_gesture.setPixmap(QtGui.QPixmap(f"gesture_icons/{gesture_class}.jpg"))
            subWin.img_update(f"gesture_icons/{gesture_class}.jpg")
            QtCore.QTimer.singleShot(2000, cleartjpg)

        printlog("输出:" + result, fontcolor='blue')
        return result

    except Exception as e:
        printlog(f"推理过程出错: {str(e)}", fontcolor='red')
        return gesturedict['7']  # 返回'NO'
```

## Batch维度处理说明

### 特征维度变化过程

**原始特征形状:**
- `ART_feature`: (64, 64, 64) - 3D特征
- `DT_feature`: (64, 64) - 2D特征
- `ERT_feature`: (64, 64, 64) - 3D特征
- `RT_feature`: (64, 64) - 2D特征
- `RDT_feature`: (64, 64, 64) - 3D特征

**data_add_channel处理后:**
- `ART_processed`: (64, 1, 64, 64) - 添加通道维度
- `DT_processed`: (1, 64, 64) - 添加通道维度
- `ERT_processed`: (64, 1, 64, 64) - 添加通道维度
- `RT_processed`: (1, 64, 64) - 添加通道维度
- `RDT_processed`: (64, 1, 64, 64) - 添加通道维度

**添加batch维度后 (unsqueeze(0)):**
- `ART`: [1, 64, 1, 64, 64] - batch_size=1
- `DT`: [1, 1, 64, 64] - batch_size=1
- `ERT`: [1, 64, 1, 64, 64] - batch_size=1
- `RT`: [1, 1, 64, 64] - batch_size=1
- `RDT`: [1, 64, 1, 64, 64] - batch_size=1

### 输出处理

模型输出也会包含batch维度，函数会自动处理：
- 预测类别: 从 `[1]` 形状提取为标量
- 置信度分数: 从 `[1]` 形状提取为标量

## 注意事项

1. **模型配置参数**: 确保 `create_model_args()` 中的参数与训练时的配置完全一致
2. **特征形状**: 根据实际的特征数据调整 `input_feature_shapes`
3. **Batch维度**: 函数自动处理batch维度，无需手动添加
4. **置信度阈值**: 根据模型性能调整 `confidence_threshold`
5. **错误处理**: 函数包含了完整的错误处理机制
6. **性能优化**: 模型只需加载一次，后续推理会很快

## 性能优化建议

1. **模型预热**: 在程序启动时进行一次推理预热
2. **GPU加速**: 如果有GPU，确保模型和数据都在GPU上
3. **内存管理**: 使用 `torch.no_grad()` 减少内存占用
4. **异步处理**: 可以将推理放在单独的线程中执行
5. **批处理**: 如果需要处理多个样本，可以批量处理

## 测试建议

1. 先用模拟数据测试函数是否正常工作
2. 检查特征数据的形状和类型是否正确
3. 验证推理速度是否满足实时要求
4. 测试不同置信度阈值的效果
