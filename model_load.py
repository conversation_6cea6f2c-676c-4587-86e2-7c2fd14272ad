
from models.model import MultiViewFeatureFusion
import torch.nn as nn
import torch
import numpy as np

def minmaxscaler(data):
    mean = data.min()
    var = data.max() 
    return (data - mean)/(var-mean)

def initialize_model(args, num_classes):
    """
    Initialize the MultiViewFeatureFusion model based on the given architecture.

    Args:
        args: Arguments containing configurations and hyperparameters.
        num_classes: Number of classes.

    Returns:
        Initialized MultiViewFeatureFusion model.
    """

    # Get other fusion-related parameters from args or set defaults
    if args.fusion_mode == 'concatenate': 
        method = getattr(args, 'method', 'concat')
    elif args.fusion_mode == 'attention':  
        method = getattr(args, 'method', 'DScombine')

        
    bottleneck_dim = getattr(args, 'bottleneck_dim', None)

    # Create input_feature_shapes dictionary (should be provided in args)
    input_feature_shapes = args.input_feature_shapes  # Must be a dict mapping feature names to shapes

    # Initialize the MultiViewFeatureFusion model
    model = MultiViewFeatureFusion(
        backbone=args.backbone,
        cnn_output_size=args.cnn_output_size,
        hidden_size=args.hidden_size,
        rnn_type=args.rnn_type,
        lstm_layers=args.lstm_layers,
        bidirectional=args.bidirectional,
        fc_size=args.fc_size,
        num_domains=args.num_domains,
        input_feature_shapes=input_feature_shapes,
        fusion_mode=args.fusion_mode,
        method=method,
        is_sharedspecific=args.is_sharedspecific,
        bottleneck_dim=bottleneck_dim,
        selected_features=args.selected_features,
        is_domain_loss=args.is_domain_loss,
        num_classes=num_classes,
    )

    # Add a classifier layer to output logits for num_classes
    if method != 'DScombine':
        model.classifier = nn.Linear(args.fc_size, num_classes)

    return model


def model_load(args, best_model_path, device):
    model_info = torch.load(best_model_path)
    model = initialize_model(args, 7)
    model.load_state_dict(model_info["state_dict"])
    model.to(device)
    model.eval()    
    return model

def data_add_channel(ART_feature, DT_feature, ERT_feature, RT_feature, RDT_feature):
        
    ART_feature = np.expand_dims(ART_feature, axis=1)
    DT_feature = np.expand_dims(DT_feature, axis=0)
    ERT_feature = np.expand_dims(ERT_feature, axis=1)
    RT_feature = np.expand_dims(RT_feature, axis=0)
    RDT_feature = np.expand_dims(RDT_feature, axis=1)

    return ART_feature, DT_feature, ERT_feature, RT_feature, RDT_feature


def validate_model_feature(args, val_loader, num_classes, model, criterion):
    device = args.device
    model.eval()
    with torch.no_grad():
        for batch_idx, data in enumerate(val_loader):
            # Unpack data
            # data[0] is a list of feature tensors
            # data[1] is the target labels
            # data[2] is file_paths
            features_list, targets, file_paths, domain_labels = [
                [d.to(args.device) for d in data[:-3]], data[-3],data[-2],data[-1]]
            
            targets = targets.to(device)

            # Create a features dictionary mapping feature names to tensors
            features_dict = {
                feature_name: feature.to(device)
                for feature_name, feature in zip(model.selected_features, features_list)
            }

            # Create a features dictionary mapping feature names to tensors
            features_dict = {
                feature_name: feature
                for feature_name, feature in zip(args.optional_features, features_list)
            }
            # 从 features_dict 中选择名在 args.selected_features 中的特征  
            selected_features_dict = {  
                feature_name: features_dict[feature_name]  
                for feature_name in model.selected_features  
                if feature_name in features_dict  
            }  
            # Normalize features
            selected_features_dict = {k: minmaxscaler(v) for k, v in selected_features_dict.items()}

            # Forward pass
            # Forward pass
            if args.method=='DScombine':
                fused_features, alphas, alpha_combined, u_a, u_tensor = model(selected_features_dict) 
                fused_features = torch.cat(fused_features, dim=-1) # 合并所有视角的特征
                weights = 1 - u_tensor
                outputs = alpha_combined - 1
            else:
                outputs, weights, fused_features = model(selected_features_dict)  # Outputs are logits of shape [batch_size, num_classes]

            file_paths_list.extend(file_paths)
            fused_features_list.extend(fused_features.to('cpu'))
    return outputs