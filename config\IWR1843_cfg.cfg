% ***************************************************************
% Created for SDK ver:03.05
% Created using Visualizer ver:3.5.0.0
% Frequency:77
% Platform:xWR18xx
% Scene Classifier:best_range
% Azimuth Resolution(deg):15
% Range Resolution(m):0.47
% Maximum unambiguous Range(m):50
% Maximum Radial Velocity(m/s):18.28
% Radial velocity resolution(m/s):2.29
% Frame Duration(msec):50
% RF calibration data:None
% Range Detection Threshold (dB):15
% Doppler Detection Threshold (dB):15
% Range Peak Grouping:enabled
% Doppler Peak Grouping:enabled
% Static clutter removal:disabled
% Angle of Arrival FoV: Full FoV
% Range FoV: Full FoV
% Doppler FoV: Full FoV
% ***************************************************************
sensorStop
flushCfg

# 1:frame based chirps, 2:continuous chirp, 3:adv frame config [1/3]
dfeDataOutputMode 1

#* <rxChanEn><TxChanEn><0(cascading)>[15][x][0]
#***[para 1 2] rx/tx Channel En <0x1111 means four devices enable>
channelCfg 15 5 0

#* <numADCBits 0:12bit,1:14bit,2:16bit>[2]
# <adcOutputFmt 0:real,1:complex1,2:complex2>[1/2]
#[para 1] bit of one data
#[para 2] complex or real
adcCfg 2 1

#* <subFrameIdx>[-1]
# <adcOutFmt 0:Complex,1:Real>[0]
# <sampleSwap 0:I in LSB Q in MSB,1 otherwise>[1]
# <ChanInterleave 0:Interleaved,1:NonItl>[1]
# <ChirpThreshold..MUST be 1 for LVDS>[1]
adcbufCfg -1 0 1 1 1

# ===========================================================================

#* <profID> <startFreq:GHz> <ideleTime:us> <adcStartTime:us>
# <rampEndTime:us> <txOutPower>[0] <txPhaseShift>[0]
# ***[para 8]<freqSlopeConst:MHz/us> <txStartTime:us> <numAdcSample>
# ***[para 10]Samples num
# ***[para 11]Samples rate
# ***[para 12]CornerFreq1 <0:175kHz 1:235kHz 2:350kHz 3:700kHz>
# ***[para 13]CornerFreq2 <0:175kHz 1:235kHz 2:350kHz 3:700kHz>
# <digOutSampleRate:ksps>
# <hpfCornerFreq1 0:175KHz,1:235,2:350,3:700>
# <hpfCornerFreq2 0:350KHz,1:700,2:1400,3:2800>
# <rxGain>
profileCfg 0 77 8 7 18.64 0 0 30 1 133 12499 0 0 30

#* <startIdx> <endIdx> <profID>
# <startFreqVar>[0] <freqSlopeVar>[0] <idleTimeVar>[0]
# <AdcStartTimeVar>[0] <txEnableMask>
chirpCfg 0 0 0 0 0 0 0 1
chirpCfg 1 1 0 0 0 0 0 4
#* <startIdx> <endIdx>
# <loopNum>[should be 4x] <frameNum> <framePerio:ms>
# <trigSel 1:Software,2:Hardware>[1] <frameTrigDelay:ms>
#***[para 3]number of loops(num of chirps in a frame)
#***[para 4]num of frames range(0~65535)--0 means infinite
#***[para 5]frame periodicity in ms (float values allowed)
frameCfg 0 1 16 0 50 1 0

# ===========================================================================

#* <Ignored>[0] <AdcMode 0:Regular,1:LP Mode>
lowPower 0 0

# <subFrameIdx For Demo Visualizer,streamed on UART not LVDS>[-1]
# <detectedObj 0:disable,1:enable Point Cloud&side info,2:enable PC>
# <logMagRange 0:disable,1:enable>
# <noiseProf 0:disable,1:enable>[0]
# <rangeAziHeatmap 0,1>[0]
# <rangeDFSHeatmap 0,1>[0]
# <stasInfo 0,1>[0]
guiMonitor -1 1 1 0 0 0 1

# Must be two lines
cfarCfg -1 0 2 8 4 3 0 15 1
cfarCfg -1 1 0 4 2 3 1 15 1

# <> <disabled>[0] ...
multiObjBeamForming -1 1 0.5
# <> <disabled>[0]
clutterRemoval -1 0
# <> <disabled>[0] ...
calibDcRangeSig -1 0 -5 8 256
# <> <disabled>[0]
extendedMaxVelocity -1 0

#* <subFramIdx>[-1] <enableHeader 0,1>[0]
# <dataFmt 0:HW disable,1:ADC,2:CP_ADC_CQ>[1] <enableSW 0,1>[0]
lvdsStreamCfg -1 0 1 0

# <rangeBias> <I/Q Bias compen for 2Tx*4Rx>
compRangeBiasAndRxChanPhase 0.0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0 1 0
# <disable compRangeBiasAndRxChanPhase>[0] ...
measureRangeBiasAndRxChanPhase 0 1.5 0.2

CQRxSatMonitor 0 3 4 31 0
CQSigImgMonitor 0 65 4
# <disable CQRxSatMonitor 0,1>[0] <disable CQSigImgMonitor 0,1>[0]
analogMonitor 0 0

# <subFrameIdx>[-1] <minAzimuthDeg> <maxAzimuthDeg>
# <minElevationDeg> <maxElevationDeg>
aoaFovCfg -1 -90 90 -90 90

# <subFrameIdx>[-1] <0:range,1:Doppler>
# <min> <max>
cfarFovCfg -1 0 0 49.99
cfarFovCfg -1 1 -18.28 18.28

calibData 0 0 0

sensorStart
